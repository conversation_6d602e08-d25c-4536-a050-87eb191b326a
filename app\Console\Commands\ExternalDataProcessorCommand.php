<?php

/**
 * 外挂数据处理脚本
 * @desc 外挂数据处理脚本
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/06/04
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\StarRocks\ExceptionStreamAll;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ExternalDataProcessorCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'external:data:processor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '外挂数据处理脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            // 获取当前时间的前10分钟
            $tenMinutesAgo = Carbon::now()->subMinutes(10);
            // 获取外挂数据
            $query = ExceptionStreamAll::query()
                ->select(['extra_app_id', 'exception_merge_id', 'stream_date', 'stream_time', 'event_time', 'dev_create_time', 'server_dev_str', 'os_type', 'version', 'os_version', 'app_version', 'is_success', 'operate_status', 'release_store', 'manufacturer', 'device_model', 'account_id', 'role_id', 'role_name', 'ip', 'sdk_package_name', 'extra', 'explain_desc', 'basic_info_json', 'is_emulator', 'inner_version', 'exception_image'])
                ->where('stream_date', '>=', $tenMinutesAgo->toDateString())
                ->where('stream_time', '>=', $tenMinutesAgo->timestamp)
                ->where('account_id', '!=', '0')
                ->whereRaw('NULL_OR_EMPTY(account_id) = false')
                ->where('extra_app_id', 13);
            $query->where(function ($query) {
                $keywords = ['该玩家疑似开挂', '该玩家开挂修改伤害', '该玩家开挂加速'];
                foreach ($keywords as $keyword) {
                    $this->explainWhere($query, $keyword);
                }
            });
            $list = $query->getFromSR();
            // 循环处理数据
            foreach ($list as $item) {
                // 处理数据
                $item['extra'] = json_decode($item['extra'], true);
                $item['basic_info_json'] = json_decode($item['basic_info_json'], true);
                $item['explain_desc'] = urldecode($item['explain_desc']);
                dd(json_encode($item, JSON_UNESCAPED_UNICODE));
            }
        } catch (\Exception $e) {
            Log::error("执行外挂数据处理脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }

    /**
     * 错误详情的where条件
     *
     * @param $query
     * @param $value
     * @return void
     */
    private function explainWhere($query, $value): void
    {
        $columns = ['explain_desc'];
        $values = [$value, urlencode($value), rawurlencode($value)];
        foreach ($columns as $column) {
            foreach ($values as $val) {
                $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like ? ", ['%' . mb_strtolower($val) . '%'])
                    ->orWhereRaw(
                        "replace(lower({$column}), '%0a', '%20') like ? ",
                        ['%' . mb_strtolower(str_replace(["%27", "%24"], ["\'", "$"], $val)) . '%']
                    );
            }
        }
    }
}
