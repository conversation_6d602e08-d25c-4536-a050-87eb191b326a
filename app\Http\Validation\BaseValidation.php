<?php

/**
 * 验证器基础类
 * 
 */

namespace App\Http\Validation;

use App\Components\ApiResponse\StatusCode;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;

abstract class BaseValidation
{
    /**
     * 规则
     *
     * @var array
     */
    protected $rules = [];

    /**
     * 静态创建方法
     *
     * @return BaseValidation
     */
    public static function build(): BaseValidation
    {
        return new static();
    }

    /**
     * 校验
     *
     * @param array $rules
     * @return array
     */
    public function validate(array $rules = []): array
    {
        //获取请求参数
        $params = request()->all();
        //合并规则
        $rules = array_merge($this->rules, $rules);
        //提示信息
        $messages = $this->messages();
        //校验
        $validator = Validator::make($params, $rules, $messages);
        //校验失败，抛出异常
        if ($validator->fails()) {
            $this->exception($validator->errors()->first());
        }
        //校验成功，返回校验后的数据
        return $validator->validated();
    }

    /**
     * 提示信息
     *
     * @return array
     */
    protected function messages(): array
    {
        return [];
    }

    /**
     * 抛出异常
     *
     * @param $message
     */
    protected function exception($message)
    {
        $code = StatusCode::C_PARAM_ERROR;
        $message = $message ?? StatusCode::getErrorMessage($code);
        $data = [];
        throw new HttpResponseException(response()->json(compact('code', 'message', 'data'), Response::HTTP_OK));
    }
}
